Java HotSpot(TM) 64-Bit Server VM (25.202-b08) for windows-amd64 JRE (1.8.0_202-b08), built on Dec 15 2018 19:54:30 by "java_re" with MS VC++ 10.0 (VS2010)
Memory: 4k page, physical 33211400k(6133880k free), swap 49483256k(14054052k free)
CommandLine flags: -XX:-BytecodeVerificationLocal -XX:-BytecodeVerificationRemote -XX:CompressedClassSpaceSize=536870912 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=D:\github\codes\businesses-gateway\heapDump.hprof -XX:InitialHeapSize=1073741824 -XX:+ManagementServer -XX:MaxDirectMemorySize=1073741824 -XX:MaxHeapSize=1073741824 -XX:MaxMetaspaceSize=1073741824 -XX:-OmitStackTraceInFastThrow -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:TieredStopAtLevel=1 -XX:+UseCompressedClassPointers -XX:+UseCompressedOops -XX:+UseG1GC -XX:-UseLargePagesIndividualAllocation 
1.459: [GC pause (G1 Evacuation Pause) (young), 0.0040094 secs]
   [Parallel Time: 2.4 ms, GC Workers: 10]
      [GC Worker Start (ms): Min: 1458.6, Avg: 1458.7, Max: 1458.8, Diff: 0.1]
      [Ext Root Scanning (ms): Min: 0.1, Avg: 0.3, Max: 0.8, Diff: 0.7, Sum: 2.8]
      [Update RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.0]
         [Processed Buffers: Min: 0, Avg: 0.0, Max: 0, Diff: 0, Sum: 0]
      [Scan RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.0]
      [Code Root Scanning (ms): Min: 0.0, Avg: 0.4, Max: 1.8, Diff: 1.8, Sum: 3.8]
      [Object Copy (ms): Min: 0.1, Avg: 1.4, Max: 1.8, Diff: 1.7, Sum: 13.6]
      [Termination (ms): Min: 0.0, Avg: 0.0, Max: 0.1, Diff: 0.1, Sum: 0.4]
         [Termination Attempts: Min: 1, Avg: 39.6, Max: 64, Diff: 63, Sum: 396]
      [GC Worker Other (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.1]
      [GC Worker Total (ms): Min: 2.0, Avg: 2.1, Max: 2.2, Diff: 0.1, Sum: 20.7]
      [GC Worker End (ms): Min: 1460.8, Avg: 1460.8, Max: 1460.8, Diff: 0.0]
   [Code Root Fixup: 0.0 ms]
   [Code Root Purge: 0.0 ms]
   [Clear CT: 0.1 ms]
   [Other: 1.5 ms]
      [Choose CSet: 0.0 ms]
      [Ref Proc: 1.3 ms]
      [Ref Enq: 0.0 ms]
      [Redirty Cards: 0.1 ms]
      [Humongous Register: 0.0 ms]
      [Humongous Reclaim: 0.0 ms]
      [Free CSet: 0.0 ms]
   [Eden: 51.0M(51.0M)->0.0B(54.0M) Survivors: 0.0B->6144.0K Heap: 51.0M(1024.0M)->6041.3K(1024.0M)]
 [Times: user=0.00 sys=0.00, real=0.00 secs] 
1.757: [GC pause (G1 Evacuation Pause) (young), 0.0047309 secs]
   [Parallel Time: 2.9 ms, GC Workers: 10]
      [GC Worker Start (ms): Min: 1757.2, Avg: 1758.2, Max: 1759.8, Diff: 2.7]
      [Ext Root Scanning (ms): Min: 0.0, Avg: 0.2, Max: 0.8, Diff: 0.8, Sum: 1.7]
      [Update RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.0]
         [Processed Buffers: Min: 0, Avg: 0.0, Max: 0, Diff: 0, Sum: 0]
      [Scan RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.0]
      [Code Root Scanning (ms): Min: 0.0, Avg: 0.0, Max: 0.4, Diff: 0.4, Sum: 0.5]
      [Object Copy (ms): Min: 0.0, Avg: 1.3, Max: 2.4, Diff: 2.4, Sum: 13.2]
      [Termination (ms): Min: 0.0, Avg: 0.1, Max: 0.1, Diff: 0.1, Sum: 0.7]
         [Termination Attempts: Min: 1, Avg: 53.6, Max: 111, Diff: 110, Sum: 536]
      [GC Worker Other (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.1]
      [GC Worker Total (ms): Min: 0.0, Avg: 1.6, Max: 2.7, Diff: 2.7, Sum: 16.1]
      [GC Worker End (ms): Min: 1759.8, Avg: 1759.8, Max: 1759.9, Diff: 0.0]
   [Code Root Fixup: 0.0 ms]
   [Code Root Purge: 0.0 ms]
   [Clear CT: 0.1 ms]
   [Other: 1.7 ms]
      [Choose CSet: 0.0 ms]
      [Ref Proc: 1.5 ms]
      [Ref Enq: 0.0 ms]
      [Redirty Cards: 0.1 ms]
      [Humongous Register: 0.0 ms]
      [Humongous Reclaim: 0.0 ms]
      [Free CSet: 0.0 ms]
   [Eden: 54.0M(54.0M)->0.0B(460.0M) Survivors: 6144.0K->8192.0K Heap: 59.9M(1024.0M)->9277.0K(1024.0M)]
 [Times: user=0.00 sys=0.02, real=0.00 secs] 
2.336: [GC pause (Metadata GC Threshold) (young) (initial-mark), 0.0066187 secs]
   [Parallel Time: 4.4 ms, GC Workers: 10]
      [GC Worker Start (ms): Min: 2335.8, Avg: 2335.9, Max: 2336.1, Diff: 0.4]
      [Ext Root Scanning (ms): Min: 0.5, Avg: 0.6, Max: 0.9, Diff: 0.4, Sum: 6.1]
      [Update RS (ms): Min: 0.0, Avg: 0.0, Max: 0.1, Diff: 0.1, Sum: 0.1]
         [Processed Buffers: Min: 0, Avg: 0.1, Max: 1, Diff: 1, Sum: 1]
      [Scan RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.1]
      [Code Root Scanning (ms): Min: 0.0, Avg: 0.9, Max: 3.4, Diff: 3.3, Sum: 9.0]
      [Object Copy (ms): Min: 0.1, Avg: 2.4, Max: 3.2, Diff: 3.1, Sum: 23.6]
      [Termination (ms): Min: 0.0, Avg: 0.2, Max: 0.2, Diff: 0.2, Sum: 2.1]
         [Termination Attempts: Min: 1, Avg: 27.0, Max: 51, Diff: 50, Sum: 270]
      [GC Worker Other (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.1]
      [GC Worker Total (ms): Min: 3.9, Avg: 4.1, Max: 4.2, Diff: 0.4, Sum: 41.0]
      [GC Worker End (ms): Min: 2340.0, Avg: 2340.0, Max: 2340.0, Diff: 0.0]
   [Code Root Fixup: 0.1 ms]
   [Code Root Purge: 0.0 ms]
   [Clear CT: 0.3 ms]
   [Other: 1.8 ms]
      [Choose CSet: 0.0 ms]
      [Ref Proc: 1.4 ms]
      [Ref Enq: 0.0 ms]
      [Redirty Cards: 0.3 ms]
      [Humongous Register: 0.0 ms]
      [Humongous Reclaim: 0.0 ms]
      [Free CSet: 0.1 ms]
   [Eden: 73.0M(460.0M)->0.0B(602.0M) Survivors: 8192.0K->12.0M Heap: 82.1M(1024.0M)->13.1M(1024.0M)]
 [Times: user=0.08 sys=0.05, real=0.01 secs] 
2.342: [GC concurrent-root-region-scan-start]
2.345: [GC concurrent-root-region-scan-end, 0.0020934 secs]
2.345: [GC concurrent-mark-start]
2.345: [GC concurrent-mark-end, 0.0001601 secs]
2.345: [GC remark 2.345: [Finalize Marking, 0.0001239 secs] 2.345: [GC ref-proc, 0.0005102 secs] 2.345: [Unloading, 0.0019172 secs], 0.0027699 secs]
 [Times: user=0.00 sys=0.00, real=0.00 secs] 
2.348: [GC cleanup 14M->12M(1024M), 0.0005640 secs]
 [Times: user=0.00 sys=0.00, real=0.00 secs] 
2.348: [GC concurrent-cleanup-start]
2.348: [GC concurrent-cleanup-end, 0.0000064 secs]
4.030: [GC pause (Metadata GC Threshold) (young) (initial-mark), 0.0166598 secs]
   [Parallel Time: 10.2 ms, GC Workers: 10]
      [GC Worker Start (ms): Min: 4030.6, Avg: 4030.9, Max: 4031.2, Diff: 0.6]
      [Ext Root Scanning (ms): Min: 0.7, Avg: 0.9, Max: 1.3, Diff: 0.6, Sum: 9.0]
      [Update RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.0]
         [Processed Buffers: Min: 0, Avg: 0.1, Max: 1, Diff: 1, Sum: 1]
      [Scan RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.2]
      [Code Root Scanning (ms): Min: 0.1, Avg: 3.5, Max: 8.7, Diff: 8.6, Sum: 34.9]
      [Object Copy (ms): Min: 0.0, Avg: 4.8, Max: 8.3, Diff: 8.3, Sum: 48.3]
      [Termination (ms): Min: 0.0, Avg: 0.5, Max: 0.6, Diff: 0.6, Sum: 4.8]
         [Termination Attempts: Min: 1, Avg: 97.0, Max: 178, Diff: 177, Sum: 970]
      [GC Worker Other (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.2]
      [GC Worker Total (ms): Min: 9.4, Avg: 9.7, Max: 10.0, Diff: 0.7, Sum: 97.4]
      [GC Worker End (ms): Min: 4040.6, Avg: 4040.6, Max: 4040.6, Diff: 0.0]
   [Code Root Fixup: 0.1 ms]
   [Code Root Purge: 0.0 ms]
   [Clear CT: 0.2 ms]
   [Other: 6.1 ms]
      [Choose CSet: 0.0 ms]
      [Ref Proc: 5.7 ms]
      [Ref Enq: 0.0 ms]
      [Redirty Cards: 0.1 ms]
      [Humongous Register: 0.0 ms]
      [Humongous Reclaim: 0.0 ms]
      [Free CSet: 0.2 ms]
   [Eden: 346.0M(602.0M)->0.0B(580.0M) Survivors: 12.0M->34.0M Heap: 358.0M(1024.0M)->33.5M(1024.0M)]
 [Times: user=0.02 sys=0.00, real=0.02 secs] 
4.047: [GC concurrent-root-region-scan-start]
4.052: [GC concurrent-root-region-scan-end, 0.0045095 secs]
4.052: [GC concurrent-mark-start]
4.052: [GC concurrent-mark-end, 0.0001984 secs]
4.052: [GC remark 4.052: [Finalize Marking, 0.0002234 secs] 4.052: [GC ref-proc, 0.0007145 secs] 4.053: [Unloading, 0.0029287 secs], 0.0042684 secs]
 [Times: user=0.00 sys=0.00, real=0.00 secs] 
4.056: [GC cleanup 33M->33M(1024M), 0.0005929 secs]
 [Times: user=0.00 sys=0.00, real=0.00 secs] 
10.336: [GC pause (Metadata GC Threshold) (young) (initial-mark), 0.0280945 secs]
   [Parallel Time: 19.5 ms, GC Workers: 10]
      [GC Worker Start (ms): Min: 10336.5, Avg: 10336.6, Max: 10336.9, Diff: 0.4]
      [Ext Root Scanning (ms): Min: 1.5, Avg: 2.1, Max: 2.3, Diff: 0.9, Sum: 20.6]
      [Update RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.0]
         [Processed Buffers: Min: 0, Avg: 0.0, Max: 0, Diff: 0, Sum: 0]
      [Scan RS (ms): Min: 0.0, Avg: 0.0, Max: 0.0, Diff: 0.0, Sum: 0.3]
      [Code Root Scanning (ms): Min: 0.4, Avg: 4.6, Max: 16.6, Diff: 16.3, Sum: 46.5]
      [Object Copy (ms): Min: 0.2, Avg: 12.1, Max: 16.6, Diff: 16.5, Sum: 121.5]
      [Termination (ms): Min: 0.0, Avg: 0.2, Max: 0.2, Diff: 0.2, Sum: 1.9]
         [Termination Attempts: Min: 1, Avg: 407.3, Max: 554, Diff: 553, Sum: 4073]
      [GC Worker Other (ms): Min: 0.0, Avg: 0.0, Max: 0.1, Diff: 0.0, Sum: 0.3]
      [GC Worker Total (ms): Min: 18.8, Avg: 19.1, Max: 19.2, Diff: 0.4, Sum: 191.0]
      [GC Worker End (ms): Min: 10355.7, Avg: 10355.7, Max: 10355.7, Diff: 0.0]
   [Code Root Fixup: 0.3 ms]
   [Code Root Purge: 0.0 ms]
   [Clear CT: 0.5 ms]
   [Other: 7.7 ms]
      [Choose CSet: 0.0 ms]
      [Ref Proc: 6.7 ms]
      [Ref Enq: 0.1 ms]
      [Redirty Cards: 0.4 ms]
      [Humongous Register: 0.0 ms]
      [Humongous Reclaim: 0.0 ms]
      [Free CSet: 0.4 ms]
   [Eden: 530.0M(580.0M)->0.0B(567.0M) Survivors: 34.0M->47.0M Heap: 563.5M(1024.0M)->46.5M(1024.0M)]
 [Times: user=0.08 sys=0.08, real=0.03 secs] 
10.365: [GC concurrent-root-region-scan-start]
10.373: [GC concurrent-root-region-scan-end, 0.0086480 secs]
10.373: [GC concurrent-mark-start]
10.373: [GC concurrent-mark-end, 0.0001541 secs]
10.373: [GC remark 10.374: [Finalize Marking, 0.0001842 secs] 10.374: [GC ref-proc, 0.0012943 secs] 10.375: [Unloading, 0.0048764 secs], 0.0066242 secs]
 [Times: user=0.00 sys=0.00, real=0.01 secs] 
10.380: [GC cleanup 47M->47M(1024M), 0.0005450 secs]
 [Times: user=0.00 sys=0.00, real=0.00 secs] 
