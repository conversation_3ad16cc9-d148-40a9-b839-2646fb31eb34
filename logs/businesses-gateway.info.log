2025-06-19 11:25:15.918|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-19 11:25:15.936|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 10112 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-19 11:25:15.936|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-19 11:25:17.224|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-19 11:25:17.228|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-19 11:25:17.267|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-19 11:25:17.455|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-19 11:25:17.457|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-19 11:25:17.737|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=1d06f65d-b4cf-3493-b97f-f9ef47d65a04
2025-06-19 11:25:17.803|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-19 11:25:17.807|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-19 11:25:26.014|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:30.050|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:34.076|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:38.091|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:38.556|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:25:38.560|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:25:38.601|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:25:38.719|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$b858a00a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:25:38.734|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:25:38.735|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:25:38.738|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:25:42.120|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:46.122|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:50.124|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:54.127|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:25:58.129|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:26:02.132|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-19 11:26:03.841|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-19 11:26:03.842|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-19 11:26:03.842|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-19 11:26:03.842|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-19 11:26:03.842|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-19 11:26:03.843|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-19 11:26:03.843|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-19 11:26:03.843|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-19 11:26:03.843|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-19 11:26:03.843|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-19 11:26:03.843|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-19 11:26:03.844|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-19 11:26:03.844|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-19 11:26:04.165|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:04.236|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:04.300|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:04.367|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:04.410|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:04.433|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:04.509|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-19 11:26:04.510|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-19 11:26:04.518|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-19 11:26:04.519|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-19 11:26:04.975|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 10003, "msg": "当前页面太火爆了，请刷新后重试[F]"}
2025-06-19 11:26:04.977|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-19 11:26:05.129|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-19 11:26:05.292|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 22 endpoint(s) beneath base path '/actuator'
2025-06-19 11:26:05.408|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-19 11:26:05.929|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-19 11:26:06.271|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-19 11:26:06.750|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 10002
2025-06-19 11:26:07.030|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 52.185 seconds (JVM running for 53.945)
2025-06-19 11:26:07.033|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-19 11:26:07.033|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-19 11:26:07.034|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-19 11:26:07.105|WARN| N/A||main|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-19 11:26:07.112|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-19 11:26:07.150|ERROR| N/A||main|o.s.boot.SpringApplication:860|Application run failed
java.lang.IllegalStateException: Failed to execute ApplicationRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:802)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:789)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:346)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at cn.hydee.gateway.GateWayApplication.main(GateWayApplication.java:23)
Caused by: com.yxt.lang.exception.YxtBizException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.refreshData(SafeCenterDataService.java:83)
	at com.yxt.safecenter.auth.sdk.core.DataLoader.run(DataLoader.java:23)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:799)
	... 5 common frames omitted
2025-06-19 11:26:14.107|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-19 11:26:14.131|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 3132 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-19 11:26:14.132|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-19 11:26:28.429|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-19 11:26:28.461|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 26732 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-19 11:26:28.461|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-19 11:26:29.787|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-19 11:26:29.792|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-19 11:26:29.830|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-19 11:26:30.034|WARN| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:122|app.id is not available from System Property and /META-INF/app.properties. It is set to null
2025-06-19 11:26:30.037|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-19 11:26:30.273|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=1d06f65d-b4cf-3493-b97f-f9ef47d65a04
2025-06-19 11:26:30.335|WARN| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:39|Could not find meta server address, because it is not available in neither (1) JVM system property 'apollo.meta', (2) OS env variable 'APOLLO_META' (3) property 'apollo.meta' from server.properties nor (4) property 'apollo.meta' from app.properties
2025-06-19 11:26:30.338|WARN| N/A||main|c.c.f.apollo.core.MetaDomainConsts:102|Meta server address fallback to http://apollo.meta for env UNKNOWN, because it is not available in all MetaServerProviders
2025-06-19 11:26:30.340|WARN| N/A||main|c.c.framework.apollo.util.ConfigUtil:67|app.id is not set, please make sure it is set in classpath:/META-INF/app.properties, now apollo will only load public namespace configurations!
2025-06-19 11:26:34.550|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:36.574|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:38.595|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:40.599|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:40.599|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties]
2025-06-19 11:26:42.620|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:44.635|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:44.636|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.properties].
2025-06-19 11:26:44.681|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-19 11:26:45.082|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:26:45.086|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:26:45.115|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:26:45.248|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$e78bf4b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:26:45.270|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:26:45.272|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:26:45.274|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-19 11:26:46.646|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:48.654|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:50.656|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:52.659|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:54.660|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:54.660|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.yml.properties]
2025-06-19 11:26:56.666|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:26:56.667|WARN| N/A||main|c.c.f.a.internals.AbstractConfigFile:58|Init Apollo Config File failed - namespace: application.yml, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\ApolloNoAppIdPlaceHolder\config-cache\ApolloNoAppIdPlaceHolder+default+application.yml.properties].
2025-06-19 11:26:57.990|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-19 11:26:57.990|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-19 11:26:57.990|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-19 11:26:57.990|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-19 11:26:57.991|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-19 11:26:57.991|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-19 11:26:57.991|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-19 11:26:57.991|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-19 11:26:57.991|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-19 11:26:57.991|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-19 11:26:57.992|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-19 11:26:57.992|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-19 11:26:57.992|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-19 11:26:58.163|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:58.220|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:58.273|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:58.317|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:58.349|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:58.371|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-19 11:26:58.435|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-19 11:26:58.436|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-19 11:26:58.443|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-19 11:26:58.443|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-19 11:26:58.988|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 10003, "msg": "当前页面太火爆了，请刷新后重试[F]"}
2025-06-19 11:26:58.990|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-19 11:26:59.367|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-19 11:26:59.634|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 22 endpoint(s) beneath base path '/actuator'
2025-06-19 11:26:59.834|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-19 11:27:00.620|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-19 11:27:01.089|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-19 11:27:01.857|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 10002
2025-06-19 11:27:02.171|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 34.841 seconds (JVM running for 36.849)
2025-06-19 11:27:02.172|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-19 11:27:02.172|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-19 11:27:02.175|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-19 11:27:02.236|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-19 11:27:02.664|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
2025-06-19 11:27:20.670|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: ApolloNoAppIdPlaceHolder, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://apollo.meta/services/config?appId=ApolloNoAppIdPlaceHolder&ip=********** [Cause: Could not complete get operation [Cause: apollo.meta]]
